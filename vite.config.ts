import { defineConfig, loadEnv } from 'vite'
// import vue from '@vitejs/plugin-vue'
import path from 'path'
import viteCompression from 'vite-plugin-compression'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { fileURLToPath } from 'url'
// import viteImagemin from 'vite-plugin-imagemin'
// import { visualizer } from 'rollup-plugin-visualizer'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
import createVitePlugins from './vite/plugins'
import { codeInspectorPlugin } from 'code-inspector-plugin'

// https://devtools.vuejs.org/getting-started/introduction
import vueDevTools from 'vite-plugin-vue-devtools'

export default ({ mode, command }) => {
  const root = process.cwd()
  const env = loadEnv(mode, root)
  const { VITE_VERSION, VITE_PORT, VITE_API_URL } = env
  const isDev = command === 'serve'

  console.log(`🚀 API_URL = ${VITE_API_URL}`)
  console.log(`🚀 VERSION = ${VITE_VERSION}`)

  return defineConfig({
    define: {
      __APP_VERSION__: JSON.stringify(VITE_VERSION)
    },

    // 开发环境特殊配置
    ...(isDev && {
      esbuild: {
        // 开发环境保留函数名，便于调试
        keepNames: true
      }
    }),

    server: {
      port: parseInt(VITE_PORT),
      host: true,
      // 开发服务器优化
      hmr: {
        overlay: false // 关闭错误遮罩层，避免影响路由跳转
      },
      // 预热常用文件，提升首次访问速度
      warmup: {
        clientFiles: [
          './src/main.ts',
          './src/router/index.ts',
          './src/store/index.ts'
        ]
      },
      proxy: {
        '/dev-api': {
          target: VITE_API_URL,
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/dev-api/, '')
        },
        '/prod-api': {
          target: VITE_API_URL,
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/prod-api/, '')
        },
        '/img': {
          target: 'http://localhost:9097',
          changeOrigin: true
        }
      }
    },
    // 路径别名
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@views': resolvePath('src/views'),
        '@imgs': resolvePath('src/assets/img'),
        '@icons': resolvePath('src/assets/icons'),
        '@utils': resolvePath('src/utils'),
        '@stores': resolvePath('src/store'),
        '@plugins': resolvePath('src/plugins'),
        '@styles': resolvePath('src/assets/styles')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    build: {
      target: 'es2015',
      outDir: 'dist',
      chunkSizeWarningLimit: 2000,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 生产环境去除 console
          drop_debugger: true // 生产环境去除 debugger
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            // 核心框架单独打包
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // UI 框架单独打包
            'element-plus': ['element-plus'],
            // 工具库单独打包
            'utils': ['@vueuse/core', 'axios'],
            // 图表库单独打包
            'echarts': ['echarts'],
            // 编辑器单独打包
            'editor': ['@wangeditor/editor', '@wangeditor/editor-for-vue']
          }
        }
      },
      dynamicImportVarsOptions: {
        warnOnError: true,
        exclude: [],
        include: ['src/views/**/*.vue']
      }
    },

    plugins: [
      ...createVitePlugins(env, command === 'build'),
      // 自动导入 components 下面的组件，无需 import 引入
      Components({
        deep: true,
        extensions: ['vue'],
        dirs: ['src/components'], // 自动导入的组件目录
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/components.d.ts' // 指定类型声明文件的路径
      }),
      AutoImport({
        imports: ['vue', 'vue-router', '@vueuse/core', 'pinia'],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/auto-imports.d.ts',
        eslintrc: {
          // 这里先设置成true然后pnpm dev 运行之后会生成 .auto-import.json 文件之后，在改为false
          enabled: true,
          filepath: './.auto-import.json',
          globalsPropValue: true
        }
      }),
      codeInspectorPlugin({
        bundler: 'vite'
      }),
      // 打包分析
      // visualizer({
      //   open: true,
      //   gzipSize: true,
      //   brotliSize: true,
      //   filename: 'dist/stats.html' // 分析图生成的文件名及路径
      // }),
      // 压缩
      viteCompression({
        verbose: true, // 是否在控制台输出压缩结果
        disable: false, // 是否禁用
        algorithm: 'gzip', // 压缩算法,可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
        ext: '.gz', // 压缩后的文件名后缀
        threshold: 10240, // 只有大小大于该值的资源会被处理 10240B = 10KB
        deleteOriginFile: false // 压缩后是否删除原文件
      }),
      // 图片压缩
      // viteImagemin({
      //   verbose: true, // 是否在控制台输出压缩结果
      //   // 图片压缩配置
      //   // GIF 图片压缩配置
      //   gifsicle: {
      //     optimizationLevel: 4, // 优化级别 1-7，7为最高级别压缩
      //     interlaced: false // 是否隔行扫描
      //   },
      //   // PNG 图片压缩配置
      //   optipng: {
      //     optimizationLevel: 4 // 优化级别 0-7，7为最高级别压缩
      //   },
      //   // JPEG 图片压缩配置
      //   mozjpeg: {
      //     quality: 60 // 压缩质量 0-100，值越小压缩率越高
      //   },
      //   // PNG 图片压缩配置(另一个压缩器)
      //   pngquant: {
      //     quality: [0.8, 0.9], // 压缩质量范围 0-1
      //     speed: 4 // 压缩速度 1-11，值越大压缩速度越快，但质量可能会下降
      //   },
      //   // SVG 图片压缩配置
      //   svgo: {
      //     plugins: [
      //       {
      //         name: 'removeViewBox' // 移除 viewBox 属性
      //       },
      //       {
      //         name: 'removeEmptyAttrs', // 移除空属性
      //         active: false // 是否启用此插件
      //       }
      //     ]
      //   }
      // })
      // 临时禁用 Vue DevTools 以避免 macOS 权限问题
      // ...(isDev ? [vueDevTools({
      //   launchEditor: 'code',
      //   // 限制扫描范围，避免权限问题
      //   componentInspector: {
      //     toggleComboKey: 'alt-shift',
      //     toggleButtonVisibility: 'always'
      //   }
      // })] : [])
    ],
    // 预加载项目必需的组件
    optimizeDeps: {
      // 开发环境强制重新预构建依赖（解决缓存问题）
      force: isDev,
      include: [
        // 核心框架
        'vue',
        'vue-router',
        'pinia',

        // 网络请求
        'axios',

        // 工具库
        '@vueuse/core',

        // 路由相关（解决路由白屏问题）
        'nprogress',
        'nprogress/nprogress.css',

        // UI 框架核心
        'element-plus',
        'element-plus/es/locale/lang/zh-cn',
        'element-plus/es/locale/lang/en',

        // 图表库
        'echarts',
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',

        // 编辑器
        '@wangeditor/editor',
        '@wangeditor/editor-for-vue',

        // 国际化
        'vue-i18n',

        // 文件处理
        'xlsx',
        'file-saver',

        // 图片处理
        'vue-img-cutter',

        // 状态管理相关
        'pinia/dist/pinia.esm-browser.js'
      ],
      exclude: [
        // 排除不需要预构建的依赖
        '@iconify/json',
        'virtual:*'
      ]
    },
    css: {
      preprocessorOptions: {
        // sass variable and mixin
        scss: {
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api'],
          quietDeps: true,
          verbose: false,
          additionalData: `
            @use "@styles/variables.scss" as *; @use "@styles/mixin.scss" as *;
          `
        }
      },
      postcss: {
        plugins: [
          tailwindcss,
          autoprefixer,
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    }
  })
}

function resolvePath(paths: string) {
  return path.resolve(__dirname, paths)
}
