<template>
  <ElSubMenu v-if="hasChildren" :index="item.path || item.meta.title">
    <template #title>
      <i
        class="menu-icon iconfont-sys"
        :style="{ color: theme?.iconColor }"
        :class="item.meta.icon"
      ></i>
      <span>{{ proxy.$t(`${item.meta.menuCode}`) }} </span>
      <div v-if="item.meta.showBadge" class="art-badge art-badge-horizontal" />
      <div v-if="item.meta.showTextBadge" class="art-text-badge">
        {{ item.meta.showTextBadge }}
      </div>
    </template>

    <!-- 递归调用自身处理子菜单 -->
    <HorizontalSubmenu
      v-for="child in filteredChildren"
      :key="child.path"
      :item="child"
      :theme="theme"
      :is-mobile="isMobile"
      :level="level + 1"
      @close="closeMenu"
      :basePath="resolvePath(item.path)"
    />
  </ElSubMenu>

  <ElMenuItem
    v-else-if="!item.meta.isHide"
    :basePath="resolvePath(item.path)"
    :index="item.path || item.title"
    :level-item="level + 1"
    @click="goPage(item)"
  >
    <i
      class="menu-icon iconfont-sys icon-width"
      :style="{ color: theme?.iconColor }"
      :class="item.meta.icon"
    ></i>
    <span>{{ proxy.$t(`${item.meta.menuCode}`) }} </span>
    <div
      v-if="item.meta.showBadge"
      class="art-badge"
      :style="{ right: level === 0 ? '10px' : '20px' }"
    />
    <div v-if="item.meta.showTextBadge && level !== 0" class="art-text-badge">
      {{ item.meta.showTextBadge }}
    </div>
  </ElMenuItem>
</template>

<script lang="ts" setup>
  import { computed, getCurrentInstance, type PropType } from 'vue'
  import { AppRouteRecord } from '@/types/router'
  import { getNormalPath } from '@/utils/ruoyi'
  import { isExternal } from '@/utils/validate'
  import { router } from '@/router'

  // 获取国际化函数
  const { proxy } = getCurrentInstance()!

  const props = defineProps({
    item: {
      type: Object as PropType<AppRouteRecord>,
      required: true
    },
    theme: {
      type: Object,
      default: () => ({})
    },
    isMobile: Boolean,
    level: {
      type: Number,
      default: 0
    },
    basePath: {
      type: String,
      default: ''
    }
  })

  const emit = defineEmits(['close'])

  // 过滤后的子菜单项（不包含隐藏的）
  const filteredChildren = computed(() => {
    return props.item.children?.filter((child) => !child.meta.isHide) || []
  })

  // 计算当前项是否有可见的子菜单
  const hasChildren = computed(() => {
    return filteredChildren.value.length > 0
  })

  const goPage = (item: AppRouteRecord) => {
    closeMenu()
    router.push(item.path)
  }

  const closeMenu = () => {
    emit('close')
  }


  function resolvePath(routePath, routeQuery) {
    if (isExternal(routePath)) {
      return routePath
    }
    if (isExternal(props.basePath)) {
      return props.basePath
    }
    if (routeQuery) {
      let query = JSON.parse(routeQuery)
      return { path: getNormalPath(props.basePath + '/' + routePath), query: query }
    }
    let normalPath = getNormalPath(props.basePath + '/' + routePath)
    return normalPath
  }
</script>

<style lang="scss" scoped>
  .el-sub-menu {
    padding: 0 !important;

    :deep(.el-sub-menu__title) {
      .el-sub-menu__icon-arrow {
        right: 10px !important;
      }
    }
  }

  .menu-icon {
    margin-right: 5px;
    font-size: 16px;
  }
</style>
