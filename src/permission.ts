import AppConfig from '@/config'
import auth from '@/plugins/auth'
import usePermissionStore from '@/store/modules/permission'
import { useUserStore } from '@/store/modules/user'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'
import { isHttp } from '@/utils/validate'
import { setWorktab } from '@/utils/navigation/worktab'
import Home from '@views/index/index.vue'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getLang } from './locales' // Router
import { router } from './router'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register', '/otherLogCallback', '/upload']

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const { meta, path, params, query } = to
  // eslint-disable-next-line prefer-const
  let { menuCode, notTab, noCache, perms } = meta

  if (perms) {
    if (!auth.hasPermi(perms)) {
      next({ path: '/exception/403' })
      NProgress.done()
      return
    }
  }

  if (getToken()) {
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      if (usePermissionStore().menuList.length === 0) {
        isRelogin.show = true

        try {
          // 判断当前用户是否已拉取完user_info信息
          await useUserStore().getInfo()
          isRelogin.show = false
          const accessRoutes = await usePermissionStore().generateRoutes()

          // 根据roles权限生成可访问的路由表
          accessRoutes.forEach((route) => {
            if (!isHttp(route.path)) {
              route.component = Home
              router.addRoute(route) // 动态添加可访问路由表
            }
          })

          // 在动态添加路由后，再次尝试访问目标路由
          // 由于刷新页面时动态路由丢失，这里特别处理以确保可以正确加载路由
          next({ ...to, replace: true })
        } catch (err) {
          console.log(err)
          await useUserStore().logOut()
          next({ path: '/' })
          NProgress.done()
        }
      } else {
        // 此处添加一个判断，如果找不到路由但已登录，尝试访问
        // 这可能是刷新导致动态路由丢失的情况
        if (to.matched.length === 0) {
          // 如果是刷新页面导致的路由不匹配，尝试重新生成路由
          try {
            const accessRoutes = await usePermissionStore().generateRoutes()
            accessRoutes.forEach((route) => {
              if (!isHttp(route.path)) {
                route.component = Home
                router.addRoute(route)
              }
            })
            // 重新尝试导航到目标路由
            next({ ...to, replace: true })
          } catch (error) {
            console.error('重新生成路由失败:', error)
            next({ path: '/dashboard/console' })
          }
        } else {
          next()
        }
      }
    }
  } else {
    useUserStore().initI18nMessages(true)
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
  // 设置标签页和网页标题
  if (!notTab && menuCode) {
    setWorktab({
      title: menuCode as string,
      path,
      name: menuCode as string,
      keepAlive: noCache as boolean,
      params,
      query,
      meta
    })
  }

  if (menuCode) {
    document.title = `${getLang(menuCode as string)} - ${AppConfig.systemInfo.name as string}`
  } else {
    document.title = `${AppConfig.systemInfo.name as string}`
  }
})

// 确保每次路由结束时NProgress也结束
router.afterEach(() => {
  NProgress.done()
})

// 添加路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})
