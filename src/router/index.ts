import type { App } from 'vue'
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import Home from '@views/index/index.vue'
import * as NProgress from 'nprogress'

/** 顶部进度条配置 */
NProgress.configure({
  easing: 'ease',
  speed: 600,
  showSpinner: false,
  trickleSpeed: 200,
  parent: 'body'
})

/** 扩展的路由配置类型 */
export type AppRouteRecordRaw = RouteRecordRaw & {
  hidden?: boolean
}

/** 首页路径常量 */
export const HOME_PAGE = '/dashboard/console'

/** 静态路由配置 */
export const routes: AppRouteRecordRaw[] = [
  { path: '/', redirect: HOME_PAGE },
  {
    path: '/dashboard',
    component: Home,
    meta: {
      menuCode: 'menu.personal.center'
    },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: () => import(`@views/dashboard/console/index.vue`),
        meta: {
          menuCode: 'menu.dashboard',
          keepAlive: false,
          fixedTab: true
        }
      },
      {
        path: 'analysis',
        name: 'Analysis',
        component: () => import(`@views/dashboard/analysis/index.vue`),
        meta: {
          title: '分析页',
          title_en: 'Analysis',
          keepAlive: false
        }
      },
      {
        path: 'user',
        name: 'Users',
        component: () => import('@views/system/user-center/index.vue'),
        meta: {
          title: '用户信息',
          menuCode: 'menu.user.info'
        }
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@views/auth/login/index.vue'),
    meta: {
      title: '登录',
      isHideTab: true
    }
  },
  {
    path: '/upload',
    component: () => import('@views/system/oss/upload.vue'),
    meta: {
      title: '上传图片',
      notTab: true
    }
  },
  {
    path: '/otherLogCallback',
    component: () => import('@/views/auth/login/otherLogCallback'),
    notTab: true,
    title: '登录'
  },
  {
    path: '/exception',
    component: Home,
    meta: {
      title: '异常页面',
      title_en: 'Exception',
      keepAlive: true,
      menuCode: 'exception.dashboard'
    },
    children: [
      {
        path: '403',
        component: () => import('@/views/exception/403/index.vue'),
        meta: {
          title: '403',
          title_en: '403',
          keepAlive: true,
          menuCode: 'exception.403'
        }
      },
      {
        path: '404',
        component: () => import('@views/exception/404/index.vue'),
        meta: {
          title: '404',
          title_en: '404',
          keepAlive: true,
          menuCode: 'exception.404'
        }
      },
      {
        path: '500',
        component: () => import('@views/exception/500/index.vue'),
        meta: {
          title: '500',
          title_en: '500',
          keepAlive: true,
          menuCode: 'exception.404'
        }
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Home,
    hidden: true,
    permissions: ['system:user:edit'],
    meta: {
      menuCode: 'menu.user'
    },
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user', menuCode: 'menu.role.auth' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Home,
    hidden: true,
    permissions: ['system:role:edit'],
    meta: {
      menuCode: 'menu.role'
    },
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role', menuCode: 'menu.user.auth' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Home,
    hidden: true,
    permissions: ['system:dict:list'],
    meta: {
      menuCode: 'menu.dict'
    },
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict', menuCode: 'menu.dict.data' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Home,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Home,
    hidden: true,
    permissions: ['tool:gen:edit'],
    meta: {
      menuCode: 'menu.gen'
    },
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen', menuCode: 'code.config' }
      }
    ]
  }
] as AppRouteRecordRaw[]

export const router = createRouter({
  history: createWebHistory(), // history 模式
  routes: routes, // 路由表
  scrollBehavior: () => ({ left: 0, top: 0 }) // 页面滚动行为
})

// 需要权限的路由
export const roleRoutes: AppRouteRecordRaw[] = []

export function initRouter(app: App<Element>) {
  app.use(router)
}

// 主页路径，默认使用菜单第一个有效路径，配置后使用此路径
export const HOME_PAGE_PATH = ''
